import json
import pandas as pd
import hashlib
import copy
import typing
from paths import AlphaPath
import numpy as np

class Alpha:
    def __init__(self, payload: dict = {}) -> None:
        self.payload = copy.copy(payload)
        self.name = None
        self.set_name()
        self.result = None
        self.location = None

    @classmethod
    def load(cls, path: str) -> typing.Self:
        with open(path, 'r') as h:
            tmp = json.load(h)
        rt_class = cls(tmp["payload"])
        rt_class.name = tmp["name"]
        rt_class.result = tmp["result"]
        rt_class.location = tmp["location"]
        return rt_class

    def save(self, path: str) -> None:
        f = open(path, 'w')
        json.dump({"result": self.result,
                   "payload": self.payload,
                   "name": self.name,
                   "location": self.location}, f)
        f.close()

    def get_pnl(self) -> pd.DataFrame:
        def __get_pnl(id):
            path = f"{AlphaPath.PNL}/{id}"
            with open(path, "r") as f:
                pnl = json.load(f)
            text = pd.DataFrame(pnl["records"]).T
            return (text.iloc[1] - text.iloc[1].shift(1)).dropna()

        try:
            return __get_pnl(self.result["id"])
        except:
            if self.result is None:
                print("This Alpha didn't be simulate yet")
            else:
                print("You should get this alpha by get_pnl under Simulator which is Simulator.get_pnl(alpha_id)")
                print("Please make sure your session is valid")

    def set_name(self) -> None:
        self.name = hashlib.sha256((str(self.payload["settings"]) + self.payload["regular"]).encode("utf-8")).hexdigest()

    def get_name(self) -> str:
        return self.name
    
    def get_regular(self) -> str:
        return self.payload["regular"]
    
    def get_sharpe(self) -> float:
        value = self.result["is"]["sharpe"]
        return value if value is not None else np.nan

    def get_fitness(self) -> float:
        value = self.result["is"]["fitness"]
        return value if value is not None else np.nan

    def get_turnover(self) -> float:
        value = self.result["is"]["turnover"]
        return value if value is not None else np.nan

    def get_drawdown(self) -> float:
        value = self.result["is"]["drawdown"]
        return value if value is not None else np.nan
    
    def get_id(self) -> str:
        return self.result["id"]
    
    def get_operator_count(self) -> int:
        return self.result["regular"]["operatorCount"]
    
    def get_result(self) -> dict:
        return self.result
    
    def get_status(self) -> str:
        return self.result["status"]
    
    def get_checks(self) -> dict:
        return self.result["is"]["checks"] if self.result is not None else {}

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Alpha):
            return NotImplemented
        return self.name == other.name

    def __hash__(self) -> int:
        return hash(self.name)

    def __getitem__(self, key: str) -> typing.Any:
        vaild_key = ["sharpe", "turnover", "fitness", "drawdown", "id"]
        if key not in vaild_key:
            print("wrong key")
            print(f"Your key should lies in {vaild_key}")
            return

        try:
            return self.result[key]
        except:
            return self.result["is"][key]
