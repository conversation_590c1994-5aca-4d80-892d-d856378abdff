import os
import json
import pickle
import requests
import time
from time import sleep
from urllib.parse import urljoin
from datetime import datetime
import telebot
import random
# from telegram import BotCommand
from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton

from alpha import Alpha
from custom_op import replace_custom_op
from alpha_utils import alpha_regular_dce
from paths import AlphaPath

from dotenv import load_dotenv
import logging
import threading

load_dotenv()


WQ_ACCOUNT = os.getenv("WQ_ACCOUNT")
WQ_PASSWORD = os.getenv("WQ_PASSWORD")
TELEGRAM_BOT_API_TOKEN = os.getenv("TELEGRAM_BOT_API_TOKEN")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")
TIME_OUT_HOURS = 4  # session 快取有效時間（小時）


class TelegramBot:

    def __init__(self, worker_instance=None) -> None:
        self.apitoken = TELEGRAM_BOT_API_TOKEN
        self.chatid = TELEGRAM_CHAT_ID
        self.apiurl = f'https://api.telegram.org/bot{self.apitoken}/sendMessage'
        self.bot = telebot.TeleBot(self.apitoken)
        self.worker_instance = worker_instance
        self.sess_instance = None
        self._setup_command_handlers()
        self.status_thread = None
        self.status_thread_running = False
        self._setup_commands()
        self.auth_event = threading.Event()
        self.auth_lock = threading.Lock()
        self._setup_callback_handlers()  # 新增回調處理

    def set_session_instance(self, sess_instance):
        self.sess_instance = sess_instance

    def _setup_command_handlers(self):
        @self.bot.message_handler(commands=['refresh_session', 'refresh'])
        def handle_refresh_session(message):
            if self.auth_lock.locked():
                self.send_message('登入進行中，請等待...')
                return
            def refresh_thread_func():
                with self.auth_lock:
                    self.send_message('開始手動刷新 session...')
                    if self.sess_instance:
                        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        response = self.sess_instance.sess.post(f'{self.sess_instance._url}/authentication')
                        if response.status_code == 401 and response.headers.get("WWW-Authenticate") == "persona":
                            persona_url = urljoin(response.url, response.headers["Location"])
                            markup = InlineKeyboardMarkup()
                            markup.add(InlineKeyboardButton("我已完成登入", callback_data="auth_completed"))
                            self.bot.send_message(self.chatid, f"請完成生物辨識登入: {persona_url}", reply_markup=markup)
                            self.auth_event.clear()
                            if not self.auth_event.wait(timeout=180):  # 等待用戶確認等待3分鐘
                                self.send_message('登入超時，請重新執行 /refresh')
                                return
                            post_response = self.sess_instance.sess.post(persona_url)
                            print(persona_url)
                            print(post_response)
                            if post_response.ok:
                                self.sess_instance.save_login_cookies()
                                self.sess_instance.reset_auth_status()  # 釋放鎖並重置標記
                                self.send_message('Session 刷新成功！')
                            else:
                                self.send_message('Session 刷新失敗，請重試。')
                        else:
                            self.send_message('無法啟動登入流程。')
                    else:
                        self.send_message('無法獲取 session 實例')
            refresh_thread = threading.Thread(target=refresh_thread_func)
            refresh_thread.start()

        @self.bot.message_handler(commands=['status', 'stat'])
        def handle_status(message):
            status_msg = self._get_system_status()
            self.send_message(status_msg)

        @self.bot.message_handler(commands=['help', 'start'])
        def handle_help(message):
            help_text = '''
可用指令：
/refresh - 手動刷新 session
/status - 查看狀態
/help - 幫助
            '''
            self.send_message(help_text)

    def _setup_commands(self):
        commands = [
            telebot.types.BotCommand('refresh', '手動刷新 session'),
            telebot.types.BotCommand('status', '查看系統狀態'),
            telebot.types.BotCommand('help', '顯示幫助資訊')
        ]
        self.bot.set_my_commands(commands)

    def _setup_callback_handlers(self):
        @self.bot.callback_query_handler(func=lambda call: True)
        def callback_query(call):
            if call.data == "auth_completed":
                self.bot.answer_callback_query(call.id, "登入確認中...")
                self.auth_event.set()

    def _get_system_status(self) -> str:
        pending_count = len(os.listdir(AlphaPath.PENDING)) if os.path.exists(AlphaPath.PENDING) else 0
        running_count = len(os.listdir(AlphaPath.RUNNING)) if os.path.exists(AlphaPath.RUNNING) else 0

        session_status = self._check_session_status()
        login_duration_str = self._get_login_duration()
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return f'''

機器狀態：
時間: {current_time}
等待中: {pending_count}
運行中: {running_count}
Session: {session_status}
{login_duration_str}
        '''.strip()

    def _get_login_duration(self) -> str:
        if self.sess_instance and self.sess_instance.login_time:
            duration = datetime.now() - self.sess_instance.login_time
            hours, remainder = divmod(duration.total_seconds(), 3600)
            minutes, _ = divmod(remainder, 60)
            return f"已登入 {int(hours)} 小時 {int(minutes)} 分鐘"
        return ""

    def _check_session_status(self) -> str:
        if self.sess_instance:
            _SIM_API = "https://api.worldquantbrain.com/simulations"
            # 直接使用 sess 而不經過 _handle_request，避免觸發認證機制
            response = self.sess_instance.sess.options(_SIM_API)
            if response.status_code == 200:
                return '有效'
            elif response.status_code == 401:
                return '已過期'
            else:
                return f'未知 ({response.status_code})'
        return '無法檢查'

    def start_status_monitoring(self):
        if self.status_thread_running:
            return
        self.status_thread_running = True

        def status_job():
            while self.status_thread_running:
                sleep(600) # 測試用每10分鐘檢查一次
                if self.status_thread_running:
                    status_msg = self._get_system_status()
                    self.send_message(f'定時狀態報告：\n{status_msg}')

        self.status_thread = threading.Thread(target=status_job, daemon=True)
        self.status_thread.start()
        self.send_message('狀態監控已啟動，每小時推送報告')

    def stop_status_monitoring(self):
        self.status_thread_running = False
        if self.status_thread:
            self.status_thread.join(timeout=5)
        self.send_message('狀態監控已停止')

    def send_message(self, message: str) -> None:
        self.bot.send_message(chat_id=self.chatid, text=message)

    def send_telegram_notification(self, message: str, persona_url: str) -> None:
        markup = InlineKeyboardMarkup()
        markup.add(InlineKeyboardButton("Perform Biometric Authentication", callback_data="auth_completed"))
        if persona_url is not None:
            self.bot.send_message(chat_id=self.chatid, text=f"{message}[URL]{persona_url}", reply_markup=markup)
        else:
            self.bot.send_message(chat_id=self.chatid, text=f"now is {message} please login", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "auth_completed")
        def callback_query(call):
            if call.data == "auth_completed":
                self.bot.answer_callback_query(call.id, "Continuing the login process...")
                self.auth_event.set()  # 設置事件，表示用戶已確認

    def wait_for_auth_confirmation(self):
        self.auth_event.clear()  # 重置事件
        self.bot.infinity_polling()  # 啟動輪詢等待回調
        self.auth_event.wait()  # 等待事件被設置

    def start_command_polling(self):
        try:
            self.bot.infinity_polling(none_stop=True, interval=1, timeout=30)
        except Exception as e:
            logging.error(f'Telegram polling error: {e}')


class MyOwnSess:
    def __init__(self) -> None:
        self.__user = WQ_ACCOUNT
        self.__password = WQ_PASSWORD
        self._url = "https://api.worldquantbrain.com"
        self.sess = requests.Session()

        self.SESSION_FILE = "session.pkl"
        self.LOGIN_TIME_FILE = "login_time.pkl"
        self.login_time = None
        self.sess.auth = (self.__user, self.__password)
        self.bot = TelegramBot()
        self.bot.set_session_instance(self)
        self.load_session()
        self.auth_notification_sent = False
        self.auth_lock = threading.Lock()  # 新增認證鎖

    def save_login_cookies(self) -> None:
        if self.sess.cookies:
            with open(self.SESSION_FILE, 'wb') as f:
                pickle.dump(requests.utils.dict_from_cookiejar(self.sess.cookies), f)
            self.login_time = datetime.now()
            with open(self.LOGIN_TIME_FILE, 'wb') as f:
                pickle.dump(self.login_time, f)
            # 重新載入到當前 session
            self.load_session()
        else:
            print("No cookies to save. Authentication might have failed.")

    def load_session(self) -> None:
        if os.path.exists(self.SESSION_FILE):
            with open(self.SESSION_FILE, 'rb') as f:
                self.sess.cookies.update(requests.utils.cookiejar_from_dict(pickle.load(f)))
        if os.path.exists(self.LOGIN_TIME_FILE):
            with open(self.LOGIN_TIME_FILE, 'rb') as f:
                self.login_time = pickle.load(f)

    def login(self) -> None:
        # 移除原有邏輯，移到 /refresh 處理中
        pass  # 暫時留空，或移除方法若不再需要獨立調用

    # ---------------- 通用請求處理 ----------------
    def _handle_request(self, method: str, *args, max_retries: int = 3, **kwargs):
        # 如果已經發送過認證通知，等待認證完成
        if self.auth_notification_sent:
            with self.auth_lock:
                # 這裡會等待直到 /refresh 完成並釋放鎖
                pass

        retries = 0
        while retries < max_retries:
            try:
                response = getattr(self.sess, method)(*args, **kwargs)
            except requests.RequestException as e:
                logging.warning(f"Network error: {e}; retry {retries + 1}/{max_retries}")
                retries += 1
                sleep(5)
                continue

            if response.status_code == 401:
                logging.info("Unauthorized, re-login ...")
                if not self.auth_notification_sent:
                    self.bot.send_message('需要登入，請使用 /refresh 執行')
                    self.auth_notification_sent = True
                    # 獲取認證鎖，阻塞所有後續請求
                    self.auth_lock.acquire()
                return response

            # 如果成功請求，重置標記
            self.auth_notification_sent = False

            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 300))
                logging.warning(f"Rate limited, sleep {retry_after}s")
                sleep(retry_after)
                retries += 1
                continue

            if response.status_code in [502, 503, 504]:
                logging.warning(f"Server {response.status_code}, retry {retries + 1}/{max_retries}")
                retries += 1
                sleep(10)
                continue

            return response  # 其他狀態碼直接回傳

        raise Exception(f"Request failed after {max_retries} retries: {method} {args} {kwargs}")

    def reset_auth_status(self):
        self.auth_notification_sent = False
        # 釋放認證鎖，讓 Worker 繼續工作
        try:
            self.auth_lock.release()
        except:
            pass  # 如果鎖已經釋放，忽略錯誤

    # ---------------- 封裝 HTTP 動詞 ----------------
    def get(self, *args, **kwargs):
        return self._handle_request('get', *args, **kwargs)

    def post(self, *args, **kwargs):
        return self._handle_request('post', *args, **kwargs)

    def patch(self, *args, **kwargs):
        return self._handle_request('patch', *args, **kwargs)

    def delete(self, *args, **kwargs):
        return self._handle_request('delete', *args, **kwargs)

    def options(self, *args, **kwargs):
        return self._handle_request('options', *args, **kwargs)


class Worker:
    def __init__(self) -> None:
        self.sess = MyOwnSess()

        # 設置檔案操作專用的 logger
        self.file_logger = logging.getLogger('worker.file_operations')
        if not self.file_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.file_logger.addHandler(handler)
            self.file_logger.setLevel(logging.INFO)

    def _safe_file_operation(self, source_path: str, target_path: str, operation: str) -> bool:
        """
        安全的檔案操作，包含完整性檢查
        operation: 'move', 'copy', 'remove'
        """
        try:
            # 檢查源檔案是否存在
            if not os.path.exists(source_path):
                self.file_logger.error(f"源檔案不存在: {source_path}")
                return False

            # 檢查源檔案是否可讀
            if not os.access(source_path, os.R_OK):
                self.file_logger.error(f"源檔案無法讀取: {source_path}")
                return False

            # 檢查目標目錄是否存在
            target_dir = os.path.dirname(target_path)
            if not os.path.exists(target_dir):
                self.file_logger.error(f"目標目錄不存在: {target_dir}")
                return False

            # 檢查目標目錄是否可寫
            if not os.access(target_dir, os.W_OK):
                self.file_logger.error(f"目標目錄無法寫入: {target_dir}")
                return False

            # 記錄檔案大小以供驗證
            source_size = os.path.getsize(source_path)
            self.file_logger.info(f"準備 {operation} 檔案: {source_path} -> {target_path} (大小: {source_size} bytes)")

            return True

        except Exception as e:
            self.file_logger.error(f"檔案操作安全檢查失敗: {e}")
            return False

    def run_all(self, max_thread: int = 10, reset_or_not: bool = False, get_pnl_or_not: bool = True) -> None:
        # make sure there is basic folder needed in simulating alpha
        AlphaPath.ensure_dirs()

        # delete the alpha simulate before to ensure no simulation out of limit
        if reset_or_not:
            print("deleting the alpha in running")
            self.running = os.listdir(AlphaPath.RUNNING)
            if len(self.running) > 0:
                for name in self.running:
                    alpha = Alpha.load(os.path.join(AlphaPath.RUNNING, name))
                    alpha.result = "deleted"
                    self.sess.delete(alpha.location)

                    with open(os.path.join(AlphaPath.ERROR, name), "wb") as text:
                        pickle.dump(alpha, text)

                    os.remove(os.path.join(AlphaPath.RUNNING, name))

        # Working
        print('啟動 Telegram 監控...')
        def polling_wrapper():
            while True:
                try:
                    self.sess.bot.bot.polling(none_stop=True, interval=0, timeout=50, long_polling_timeout=45)
                except (requests.exceptions.ReadTimeout, requests.exceptions.ConnectionError) as e:
                    logging.warning(f'Polling timeout or connection error: {e}. Retrying in 5 seconds...')
                    sleep(5)
        polling_thread = threading.Thread(target=polling_wrapper, daemon=True)
        polling_thread.start()
        self.sess.bot.start_status_monitoring()
        try:
            while True:
                self.pending = os.listdir(AlphaPath.PENDING)
                self.running = os.listdir(AlphaPath.RUNNING)
                while len(self.pending) > 0 and len(self.running) < max_thread:
                    self.run_pending()

                if len(self.running) > 0:
                    self.check_runnig(get_pnl_or_not=get_pnl_or_not)
                sleep(3)
        except KeyboardInterrupt:
            print('正在停止 Worker...')
            self.sess.bot.stop_status_monitoring()
            print('Worker 已停止')

    def _select_pending_file_by_age(self):
        """基於檔案創建時間進行加權隨機選擇，越久的檔案機率越高"""
        if not self.pending:
            return None

        current_time = time.time()
        file_weights = []

        for filename in self.pending:
            file_path = os.path.join(AlphaPath.PENDING, filename)
            try:
                creation_time = os.path.getctime(file_path)
                age = current_time - creation_time  # 檔案年齡（秒）
                weight = max(1, age)  # 確保權重至少為1，避免新檔案權重為0
            except OSError:
                weight = 1  # 如果無法獲取創建時間，給予預設權重
            file_weights.append(weight)

        # 使用加權隨機選擇
        selected = random.choices(self.pending, weights=file_weights)[0]
        return selected

    def run_pending(self) -> None:
        """Simulate one alpha write simulation lacation in running/name.txt"""
        # Simulate alpha

        selected_file = self._select_pending_file_by_age()
        if selected_file is None:
            return

        pending_file_path = os.path.join(AlphaPath.PENDING, selected_file)
        print(f"simulate {selected_file} at {datetime.fromtimestamp(time.time())}")

        try:
            alpha = Alpha.load(pending_file_path)
            alpha.payload["regular"] = replace_custom_op(alpha.payload["regular"])
            alpha.payload["regular"] = alpha_regular_dce(alpha.payload["regular"]) # 在發送前對regular進行DCE
            location, error_type = self.simulate(alpha.payload)

            if error_type == 'success':
                alpha.location = location
                # 原子性檔案操作：先保存到目標位置
                running_file_path = os.path.join(AlphaPath.RUNNING, alpha.name)

                # 執行安全檢查
                if not self._safe_file_operation(pending_file_path, running_file_path, 'move'):
                    self.file_logger.error(f"安全檢查失敗，跳過檔案 {selected_file}")
                    return

                try:
                    # 保存到目標位置
                    alpha.save(running_file_path)
                    self.file_logger.info(f"已保存檔案到 RUNNING: {alpha.name}")

                    # 確認檔案成功保存並驗證完整性
                    if os.path.exists(running_file_path):
                        running_size = os.path.getsize(running_file_path)
                        pending_size = os.path.getsize(pending_file_path)

                        if running_size > 0:  # 基本完整性檢查
                            os.remove(pending_file_path)
                            self.file_logger.info(f"成功移動檔案 {selected_file} -> {alpha.name} (大小: {running_size} bytes)")
                        else:
                            self.file_logger.error(f"目標檔案大小異常，保留源檔案: {alpha.name}")
                    else:
                        self.file_logger.error(f"無法確認 {alpha.name} 已保存到 RUNNING，保留源檔案")

                except Exception as save_error:
                    self.file_logger.error(f"保存檔案時發生錯誤: {save_error}")
                    # 清理可能的不完整檔案
                    if os.path.exists(running_file_path):
                        try:
                            os.remove(running_file_path)
                            self.file_logger.info(f"已清理不完整的檔案: {running_file_path}")
                        except:
                            pass

            elif error_type == 'auth':
                print(f"模擬因認證問題暫停，檔案 {selected_file} 將等待重試")
                return

            elif error_type == 'temporary':
                print(f"模擬暫時失敗，保留檔案 {selected_file} 等待重試")
                return

            elif error_type == 'permanent':
                # 永久性錯誤：移動到錯誤資料夾而不是刪除
                print(f"模擬發生永久性錯誤，移動檔案 {selected_file} 到 ERROR")
                alpha.result = f"永久性模擬錯誤：{error_type}"
                error_file_path = os.path.join(AlphaPath.ERROR, selected_file)
                alpha.save(error_file_path)

                if os.path.exists(error_file_path):
                    os.remove(pending_file_path)
                    print(f"已移動問題檔案 {selected_file} 到 ERROR 資料夾")
                else:
                    print(f"錯誤：無法保存 {selected_file} 到 ERROR，保留源檔案")

        except Exception as e:
            print(f"處理檔案 {selected_file} 時發生錯誤: {e}")
            # 發生異常時不刪除檔案，讓它在下次重試

    def simulate(self, payload: dict) -> tuple[str | None, str]:
        """
        模擬 alpha，返回 (location, error_type)
        error_type: 'success', 'auth', 'temporary', 'permanent'
        """
        try:
            sim = self.sess.post(f"{self.sess._url}/simulations", json=payload)
        except Exception as e:
            print(f"網路請求失敗: {e}")
            return None, 'temporary'

        # 認證問題
        if sim.status_code == 401:
            return None, 'auth'

        # 成功案例
        if sim.status_code in [200, 201, 202] and "location" in sim.headers:
            return sim.headers["location"], 'success'

        # 暫時性錯誤（伺服器問題、限流等）
        if sim.status_code in [429, 500, 502, 503, 504]:
            print(f"暫時性錯誤，狀態碼: {sim.status_code}")
            return None, 'temporary'

        # 客戶端錯誤（可能是永久性問題）
        if sim.status_code in [400, 403, 404, 422]:
            print(f"客戶端錯誤，狀態碼: {sim.status_code}")
            print(f"回應內容: {sim.text}")
            return None, 'permanent'

        # 其他未知錯誤，當作暫時性處理
        print(f"未知錯誤，狀態碼: {sim.status_code}")
        print(f"回應內容: {sim.text}")
        return None, 'temporary'

    def check_runnig(self, get_pnl_or_not) -> None:
        '''check if any alpha finish simulating, if true write alpha ID in finish/name.txt'''
        for name in self.running:
            print(f"Debug: Running Check {datetime.fromtimestamp(time.time())}")
            running_file_path = os.path.join(AlphaPath.RUNNING, name)

            try:
                alpha = Alpha.load(running_file_path)
                res, error = self.get_alpha(alpha.location)

                if res != -1:
                    target_file_path = None

                    if res == "ERROR":
                        alpha.result = error
                        target_file_path = os.path.join(AlphaPath.ERROR, name)
                        alpha.location = target_file_path
                        alpha.save(target_file_path)
                        print(f"模擬錯誤，移動 {name} 到 ERROR")
                    else:
                        try:
                            alpha_url = f"{self.sess._url}/alphas/{res}"
                            alpha_result = self.sess.get(alpha_url)
                            alpha.result = json.loads(alpha_result.text)
                            target_file_path = os.path.join(AlphaPath.COMPLETE, name)
                            alpha.location = target_file_path
                            alpha.save(target_file_path)
                            print(f"模擬完成，移動 {name} 到 COMPLETE")

                            if get_pnl_or_not:
                                try:
                                    self.get_pnl(res)
                                except Exception as pnl_error:
                                    print(f"獲取 PNL 數據失敗: {pnl_error}")

                        except Exception as result_error:
                            print(f"處理模擬結果時發生錯誤: {result_error}")
                            # 如果處理結果失敗，移動到 ERROR
                            alpha.result = f"結果處理錯誤: {result_error}"
                            target_file_path = os.path.join(AlphaPath.ERROR, name)
                            alpha.location = target_file_path
                            alpha.save(target_file_path)

                    # 原子性檔案操作：確認目標檔案存在並驗證完整性後才刪除源檔案
                    if target_file_path and os.path.exists(target_file_path):
                        try:
                            # 驗證檔案完整性
                            target_size = os.path.getsize(target_file_path)

                            if target_size > 0:
                                os.remove(running_file_path)
                                self.file_logger.info(f"成功移動檔案 {name} (大小: {target_size} bytes)")
                            else:
                                self.file_logger.error(f"目標檔案大小異常，保留在 RUNNING: {name}")
                        except Exception as verify_error:
                            self.file_logger.error(f"檔案完整性驗證失敗: {verify_error}")
                    else:
                        self.file_logger.error(f"無法確認 {name} 已保存到目標位置，保留在 RUNNING")

            except Exception as e:
                print(f"檢查運行檔案 {name} 時發生錯誤: {e}")
                # 發生異常時不刪除檔案，讓它在下次檢查

    def get_pnl(self, alpha):
        url = f"{self.sess._url}/alphas/{alpha}/recordsets/pnl"
        while True:
            pnl = self.sess.get(url)
            if len(pnl.text) > 100:
                try:
                    pnl = json.loads(pnl.text)
                except:
                    print(url)
                    return
                break
            sleep(2.5)
        with open(os.path.join(AlphaPath.PNL, alpha), "w") as f:
            json.dump(pnl, f)

    def get_alpha(self, location: str) -> tuple[int | str, str | None]:
        # return location if not done return -1
        ans = self.sess.get(location)
        try:
            res = json.loads(ans.text)
        except:
            return -1, None
        if 'status' in res.keys() and res['status'] in ['ERROR', 'FAIL']:
            return 'ERROR', res

        if 'detail' in res.keys() and res["detail"] == "Not found.":
            return "ERROR", "notfound"
        if 'alpha' in res.keys():
            return res["alpha"], None
        return -1, None
