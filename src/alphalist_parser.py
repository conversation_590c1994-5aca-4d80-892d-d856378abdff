import pandas as pd
import numpy as np
from typing import List, Any, Optional, Union, Dict, Callable
from alphalist import AlphaList
from alpha import Alpha


class AlphaListParser:
    """
    AlphaList 解析器，用於將 AlphaList 中的 Alpha 物件轉換為 DataFrame
    支援透過自訂函數動態設定要提取的欄位
    """
    
    def __init__(self, alphalist: AlphaList, field_extractors: Optional[Dict[str, Callable[[Alpha], Any]]] = None):
        """
        初始化解析器
        
        Args:
            alphalist: 要解析的 AlphaList 物件
            field_extractors: 欄位提取器字典，鍵為欄位名稱，值為提取函數
                            函數應接受 Alpha 物件並返回該欄位的值
                            如果為 None，將使用預設的欄位提取器
        """
        self.alphalist = alphalist
        self.field_extractors = field_extractors or self._get_default_extractors()
    
    def _get_default_extractors(self) -> Dict[str, Callable[[Alpha], Any]]:
        """
        取得預設的欄位提取器
        
        Returns:
            預設欄位提取器字典
        """
        return {
            'name': lambda alpha: alpha.get_name(),
            'sharpe': lambda alpha: alpha.get_sharpe(),
            'fitness': lambda alpha: alpha.get_fitness(),
            'turnover': lambda alpha: alpha.get_turnover(),
        }
    
    def set_field_extractors(self, field_extractors: Dict[str, Callable[[Alpha], Any]]) -> None:
        """
        設定欄位提取器
        
        Args:
            field_extractors: 欄位提取器字典
        """
        self.field_extractors = field_extractors
    
    def add_field(self, field_name: str, extractor: Callable[[Alpha], Any]) -> None:
        """
        添加一個欄位提取器
        
        Args:
            field_name: 欄位名稱
            extractor: 提取函數，接受 Alpha 物件並返回該欄位的值
        """
        self.field_extractors[field_name] = extractor
    
    def remove_field(self, field_name: str) -> None:
        """
        移除一個欄位提取器
        
        Args:
            field_name: 要移除的欄位名稱
        """
        if field_name in self.field_extractors:
            del self.field_extractors[field_name]
    
    def get_field_names(self) -> List[str]:
        """
        取得目前設定的所有欄位名稱
        
        Returns:
            欄位名稱列表
        """
        return list(self.field_extractors.keys())
    
    def has_field(self, field_name: str) -> bool:
        """
        檢查是否有指定的欄位
        
        Args:
            field_name: 欄位名稱
            
        Returns:
            是否存在該欄位
        """
        return field_name in self.field_extractors
    
    def to_dataframe(self) -> pd.DataFrame:
        """
        將 AlphaList 轉換為 DataFrame
        
        Returns:
            包含指定欄位的 DataFrame
        """
        if not self.alphalist.alpha_list:
            print("警告：AlphaList 是空的")
            return pd.DataFrame()
        
        if not self.field_extractors:
            print("警告：沒有設定任何欄位提取器")
            return pd.DataFrame()
        
        data = []
        
        for i, alpha in enumerate(self.alphalist.alpha_list):
            row = {}
            for field_name, extractor in self.field_extractors.items():
                try:
                    value = extractor(alpha)
                    row[field_name] = value
                except Exception as e:
                    print(f"警告：無法提取 Alpha {i} 的欄位 '{field_name}': {e}")
                    row[field_name] = np.nan
            data.append(row)
        
        df = pd.DataFrame(data)
        return df
    
    def to_dataframe_safe(self, drop_invalid: bool = True) -> pd.DataFrame:
        """
        安全地轉換為 DataFrame，可選擇是否丟棄無效的 Alpha
        
        Args:
            drop_invalid: 是否丟棄所有欄位都是 NaN 的行
            
        Returns:
            DataFrame
        """
        df = self.to_dataframe()
        
        if drop_invalid and not df.empty:
            # 移除所有欄位都是 NaN 的行
            df = df.dropna(how='all')
        
        return df



# 基本欄位
def get_name(alpha: Alpha) -> str:
    return alpha.get_name()

def get_id(alpha: Alpha) -> str:
    return alpha.get_id()

def get_sharpe(alpha: Alpha) -> float:
    return alpha.get_sharpe()

def get_fitness(alpha: Alpha) -> float:
    return alpha.get_fitness()

def get_turnover(alpha: Alpha) -> float:
    return alpha.get_turnover()

def get_drawdown(alpha: Alpha) -> float:
    return alpha.get_drawdown()

def get_regular(alpha: Alpha) -> str:
    return alpha.get_regular()

def get_operator_count(alpha: Alpha) -> str:
    return alpha.get_operator_count()

def get_revising_regular(alpha: Alpha) -> str:
    #判斷sharpe是否為正，如果是正的，則回傳regular，如果是負的，則回傳則加負號
    if alpha.get_sharpe() > 0:
        return alpha.get_regular()
    else:
        return "(-1) *" + alpha.get_regular()

def get_power_pool_checks(alpha: Alpha) -> dict:
    checks = alpha.get_checks()
    check_list = ["LOW_SHARPE", "LOW_FITNESS", "HIGH_TURNOVER", "HIGH_DRAWDOWN",
            "CONCENTRATED_WEIGHT", "LOW_SUB_UNIVERSE_SHARPE", "LOW_2Y_SHARPE"]
    # 把結果都抓出來，如果結果有"FAIL"，則回傳False，否則回傳True
    for check in checks:
        if check["name"] in check_list and check["result"] == "FAIL":
            return False
    return True

def get_regular_checks(alpha: Alpha) -> dict:
    checks = alpha.get_checks()
    check_list = ["LOW_SHARPE", "LOW_FITNESS", "HIGH_TURNOVER", "HIGH_DRAWDOWN",
            "CONCENTRATED_WEIGHT", "LOW_SUB_UNIVERSE_SHARPE", "LOW_2Y_SHARPE"]
    # 把結果都抓出來，全部都PASS才回傳True
    for check in checks:
        if check["name"] in check_list and check["result"] != "PASS":
            return False
    return True

# 使用範例函數
def demo_usage():
    """
    示範如何使用 AlphaListParser
    """
    # 假設您已經有一個 AlphaList 物件
    # alphalist = AlphaList.load("path/to/your/alphalist")
    
    # 1. 使用預設欄位提取器
    # parser = AlphaListParser(alphalist)
    # df = parser.to_dataframe()
    # print("預設欄位的 DataFrame:")
    # print(df.head())
    
    # 2. 自訂欄位提取器
    # custom_extractors = {
    #     'alpha_name': lambda alpha: alpha.get_name(),
    #     'sharpe_ratio': lambda alpha: alpha.get_sharpe(),
    #     'fitness_score': lambda alpha: alpha.get_fitness(),
    #     'alpha_id': lambda alpha: alpha.get_id(),
    #     'custom_metric': lambda alpha: alpha.get_sharpe() * alpha.get_fitness(),
    #     'has_valid_result': lambda alpha: alpha.result is not None,
    # }
    # parser = AlphaListParser(alphalist, custom_extractors)
    # df_custom = parser.to_dataframe()
    # print("\n自訂欄位的 DataFrame:")
    # print(df_custom.head())
    
    # 3. 動態添加欄位
    # parser.add_field('name_length', lambda alpha: len(alpha.get_name()))
    # parser.add_field('turnover_level', lambda alpha: 'high' if alpha.get_turnover() > 0.5 else 'low')
    
    # 4. 查看所有欄位名稱
    # print("所有欄位:", parser.get_field_names())
    
    # 5. 安全模式轉換（會移除無效的 Alpha）
    # df_safe = parser.to_dataframe_safe()
    # print("\n安全模式 DataFrame:")
    # print(df_safe.head())
    
    # 6. 查看範例提取器
    # examples = parser.get_extractor_examples()
    # print("\n範例提取器:")
    # for field, code in examples.items():
    #     print(f"{field}: {code}")
    
    pass


if __name__ == "__main__":
    demo_usage() 