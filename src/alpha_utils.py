import math
import re
from typing import Set, Dict
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def percentile(li: list, percentile: float) -> float:
    n = len(li)
    idx = n * percentile
    return sorted(li)[math.floor(idx)]

# Alpha Regular Dead Code Elimination (DCE)

def alpha_regular_dce(regular: str) -> str:
    """
    Alpha Regular Dead Code Elimination (DCE)
    移除未使用的變數定義
    
    Args:
        regular: 包含變數定義和表達式的代碼字串，支援多行、單行或混合格式
        
    Returns:
        移除未使用變數後的代碼字串，保持原始格式風格
    """
    if not regular.strip():
        return ""
    
    # 智能解析：同時按換行和分號分割，處理混合格式
    statements = parse_statements(regular)
    
    if not statements:
        return ""
    
    # 找出變數定義和表達式
    definition_statements = []
    expression = None
    
    # 從後往前找，最後一個不含等號的語句是表達式
    for i in range(len(statements) - 1, -1, -1):
        stmt_content = statements[i]['content']
        if '=' not in stmt_content:
            expression = statements[i]
            definition_statements = statements[:i]
            break
    
    # 如果沒有找到表達式（所有語句都包含等號），則最後一個語句是表達式
    if expression is None:
        if statements:
            expression = statements[-1]
            definition_statements = statements[:-1]
        else:
            return ""
    
    # 解析變數定義
    definitions = {}  # variable_name -> statement
    for stmt in definition_statements:
        content = stmt['content']
        if '=' in content:
            # 移除結尾的分號並分割
            clean_content = content.rstrip(';')
            var_name, var_value = clean_content.split('=', 1)
            var_name = var_name.strip()
            var_value = var_value.strip()
            definitions[var_name] = stmt
    
    # 找出所有被使用的變數
    used_variables = find_used_variables(expression['content'], 
                                       {k: v['content'] for k, v in definitions.items()})
    
    # 構建結果：保留使用的變數定義
    result_statements = []
    for stmt in definition_statements:
        content = stmt['content']
        if '=' in content:
            var_name = content.split('=')[0].strip()
            if var_name in used_variables:
                result_statements.append(stmt)
        else:
            # 保留非變數定義語句
            result_statements.append(stmt)
    
    # 加上表達式
    result_statements.append(expression)
    
    # 重建輸出：智能保持原始格式風格
    return rebuild_output(result_statements, regular)


def parse_statements(regular: str) -> list:
    """
    智能解析代碼語句，處理混合格式（換行+分號分隔）
    
    Args:
        regular: 原始代碼字串
        
    Returns:
        語句列表，每個語句包含 {'content': str, 'original_format': str}
    """
    statements = []
    
    # 先按換行分割
    lines = regular.split('\n')
    
    for line_idx, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # 每行可能包含多個分號分隔的語句
        if ';' in line:
            # 按分號分割，但保留分號資訊
            parts = line.split(';')
            for part_idx, part in enumerate(parts):
                part = part.strip()
                if part:  # 忽略空的部分
                    # 記錄原始格式資訊
                    is_last_part = (part_idx == len(parts) - 1)
                    original_format = {
                        'line_idx': line_idx,
                        'part_idx': part_idx,
                        'has_semicolon': not is_last_part or line.rstrip().endswith(';'),
                        'is_single_line': len(lines) == 1,
                        'line_has_multiple_parts': len([p for p in parts if p.strip()]) > 1
                    }
                    
                    statements.append({
                        'content': part,
                        'original_format': original_format
                    })
        else:
            # 整行是一個語句
            original_format = {
                'line_idx': line_idx,
                'part_idx': 0,
                'has_semicolon': line.endswith(';'),
                'is_single_line': len(lines) == 1,
                'line_has_multiple_parts': False
            }
            
            statements.append({
                'content': line,
                'original_format': original_format
            })
    
    return statements


def rebuild_output(statements: list, original: str) -> str:
    """
    重建輸出，智能保持原始格式風格
    
    Args:
        statements: 處理後的語句列表
        original: 原始代碼字串
        
    Returns:
        重建後的代碼字串
    """
    if not statements:
        return ""
    
    # 分析原始格式風格
    original_lines = original.split('\n')
    is_purely_single_line = len(original_lines) == 1
    has_mixed_format = any(';' in line and line.strip() != line.strip().split(';')[0] 
                          for line in original_lines)
    
    # 如果是純單行格式，保持單行
    if is_purely_single_line:
        contents = [stmt['content'] for stmt in statements]
        # 決定是否需要分號分隔
        if len(contents) == 1:
            return contents[0]
        else:
            return '; '.join(contents)
    
    # 如果是混合格式或多行格式，嘗試智能重建
    if has_mixed_format:
        return rebuild_mixed_format(statements)
    else:
        # 純多行格式，每個語句一行
        contents = []
        for stmt in statements:
            content = stmt['content']
            # 保持原始的分號風格
            if stmt['original_format']['has_semicolon'] and not content.endswith(';'):
                content += ';'
            contents.append(content)
        return '\n'.join(contents)


def rebuild_mixed_format(statements: list) -> str:
    """
    重建混合格式的輸出
    
    Args:
        statements: 語句列表
        
    Returns:
        重建後的代碼字串
    """
    # 按照原始的行和部分索引重新組織
    lines_dict = {}
    
    for stmt in statements:
        fmt = stmt['original_format']
        line_idx = fmt['line_idx']
        
        if line_idx not in lines_dict:
            lines_dict[line_idx] = []
        
        lines_dict[line_idx].append(stmt)
    
    # 重建每一行
    result_lines = []
    for line_idx in sorted(lines_dict.keys()):
        line_statements = lines_dict[line_idx]
        
        if len(line_statements) == 1:
            # 單個語句的行
            stmt = line_statements[0]
            content = stmt['content']
            if stmt['original_format']['has_semicolon'] and not content.endswith(';'):
                content += ';'
            result_lines.append(content)
        else:
            # 多個語句的行，用分號分隔
            parts = []
            for stmt in line_statements:
                content = stmt['content']
                parts.append(content)
            result_lines.append('; '.join(parts))
    
    return '\n'.join(result_lines)


def find_used_variables(expression: str, definitions: Dict[str, str]) -> Set[str]:
    """
    遞歸找出表達式中使用的所有變數
    
    Args:
        expression: 要分析的表達式
        definitions: 變數定義字典
        
    Returns:
        使用的變數名稱集合
    """
    used = set()
    to_process = [expression]
    processed = set()
    
    while to_process:
        current_expr = to_process.pop()
        if current_expr in processed:
            continue
        processed.add(current_expr)
        
        # 提取當前表達式中的變數
        variables_in_expr = extract_variables(current_expr)
        
        for var in variables_in_expr:
            if var in definitions:
                used.add(var)
                # 獲取變數定義的右邊部分
                var_definition = definitions[var]
                if '=' in var_definition:
                    right_side = var_definition.split('=', 1)[1].strip().rstrip(';')
                    to_process.append(right_side)
    
    return used


def extract_variables(expression: str) -> Set[str]:
    """
    從表達式中提取變數名稱
    
    Args:
        expression: 要分析的表達式
        
    Returns:
        表達式中的變數名稱集合
    """
    # 使用正則表達式找出所有可能的標識符
    pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
    potential_vars = re.findall(pattern, expression)
    
    # 純粹提取所有標識符，不做語法檢查
    variables = set()
    for var in potential_vars:
        # 只排除純數字
        if not var.isdigit():
            variables.add(var)
    
    return variables

def submit_alpha(alpha_id: str):
    import time
    from src.worker import MyOwnSess

    logger.info(f"開始提交 alpha（ID: {alpha_id}）...")
    brain_session = MyOwnSess()
    logger.info("正在登入 Brain Session ...")
    brain_session.login()
    api_url = f"{brain_session._url}/alphas/{alpha_id}/submit"
    logger.info(f"提交請求至 {api_url}")
    res = brain_session.post(api_url)
    logger.info(f"提交狀態碼: {res.status_code}")
    if res.status_code != 201:
        logger.info(f"提交失敗，狀態碼: {res.status_code}")
        logger.info(f"錯誤訊息: {res.text}")
        return

    start_time = time.time()
    logger.info("等待提交結果回應 ...")
    while True:
        res = brain_session.get(api_url)
        logger.info(f"查詢狀態碼: {res.status_code}")
        if res.text != "":
            logger.info("收到回應內容：")
            logger.info(res.text)
            break
        if res.status_code == 404:
            logger.info("提交失敗，狀態碼: 404")
            logger.info("請重新提交")
            return
        logger.info("尚未收到結果，5秒後重試 ...")
        time.sleep(5)
        
    elapsed = time.time() - start_time
    logger.info(f"提交完成，總耗時 {elapsed:.2f} 秒")