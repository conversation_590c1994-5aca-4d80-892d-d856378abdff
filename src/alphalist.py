import pickle
import os
from tqdm import tqdm
import typing
import pandas as pd

from alpha import Alpha
from paths import AlphaPath
from scorer import AlphaScore


class AlphaList:
    def __init__(self):
        self.alpha_list = []
        self.alpha_names = []

    def __add__(self, other: typing.Union["AlphaList", Alpha]) -> "AlphaList":
        new_list = AlphaList()
        new_list.alpha_list = self.alpha_list[:]
        if isinstance(other, AlphaList):
            new_list.alpha_list.extend(other.alpha_list)
        elif isinstance(other, Alpha):
            new_list.alpha_list.append(other)
        else:
            return NotImplemented
        # Remove duplicates
        new_list.alpha_list = list(set(new_list.alpha_list))
        new_list.alpha_names = [alpha.name for alpha in new_list.alpha_list]
        return new_list

    def __sub__(self, other: typing.Union["AlphaList", Alpha]) -> "AlphaList":
        new_list = AlphaList()
        new_list.alpha_list = self.alpha_list[:]
        if isinstance(other, AlphaList):
            other_names = {alpha.name for alpha in other.alpha_list}
            new_list.alpha_list = [alpha for alpha in new_list.alpha_list if alpha.name not in other_names]
        elif isinstance(other, Alpha):
            new_list.alpha_list = [alpha for alpha in new_list.alpha_list if alpha.name != other.name]
        else:
            return NotImplemented
        new_list.alpha_names = [alpha.name for alpha in new_list.alpha_list]
        return new_list

    def __iadd__(self, other: typing.Union["AlphaList", Alpha]) -> "AlphaList":
        if isinstance(other, AlphaList):
            self.alpha_list.extend(other.alpha_list)
        elif isinstance(other, Alpha):
            self.alpha_list.append(other)
        else:
            return NotImplemented
        # Remove duplicates and update names
        self.alpha_list = list(set(self.alpha_list))
        self.alpha_names = [alpha.name for alpha in self.alpha_list]
        return self

    def __isub__(self, other: typing.Union["AlphaList", Alpha]) -> "AlphaList":
        if isinstance(other, AlphaList):
            other_names = {alpha.name for alpha in other.alpha_list}
            self.alpha_list = [alpha for alpha in self.alpha_list if alpha.name not in other_names]
        elif isinstance(other, Alpha):
            self.alpha_list = [alpha for alpha in self.alpha_list if alpha.name != other.name]
        else:
            return NotImplemented
        self.alpha_names = [alpha.name for alpha in self.alpha_list]
        return self
    
    def __len__(self) -> int:
        return len(self.alpha_list)
    
    def append(self, alpha: Alpha) -> None:
        """This function save the alpha into self.alpha_list and return a value with hash"""
        self.alpha_list.append(alpha)
        self.alpha_names.append(alpha.name)

    def sim_all(self) -> None:
        for alpha in self.alpha_list:
            if (
                os.path.exists(os.path.join(AlphaPath.COMPLETE, alpha.name)) or
                os.path.exists(os.path.join(AlphaPath.RUNNING, alpha.name)) or
                os.path.exists(os.path.join(AlphaPath.ERROR, alpha.name))
            ):
                continue
            else:
                alpha.save(os.path.join(AlphaPath.PENDING, alpha.name))
        self.alpha_list = []
        self.hold()
        self.get_complete_alpha()

    def get_complete_alpha(self) -> None:
        ret = []
        for name in self.alpha_names:
            if os.path.exists(os.path.join(AlphaPath.COMPLETE, name)):
                ret.append(Alpha.load(os.path.join(AlphaPath.COMPLETE, name)))
        self.alpha_list = ret

    def hold(self) -> None:
        pbar = tqdm(total=len(self.alpha_names))
        last_round = 0
        while True:
            # 取得分佈資訊
            dist = self.get_alpha_distribution()
            complete = dist['complete']
            error = dist['error']
            running = dist['running']
            pending = dist['pending']
            unknown = dist['unknown']
            # 動態顯示在進度條描述
            pbar.set_description(
                f"完成:{dist['complete']} 錯誤:{dist['error']} 執行中:{dist['running']} 等待:{dist['pending']} 未知:{dist['unknown']}"
            )
            pbar.update(complete + error - last_round)
            last_round = complete + error
            if complete + error == len(self.alpha_names):
                break

    def save(self, path: str) -> None:
        f = open(path, 'wb')
        pickle.dump(self.alpha_names, f)
        f.close()

    @classmethod
    def load(cls, path: str) -> typing.Self:
        f = open(path, 'rb')
        names = pickle.load(f)
        f.close()
        retclass = cls()
        retclass.alpha_names = names
        retclass.get_complete_alpha()
        return retclass

    def get_alpha_distribution(self) -> dict:
        """
        取得 AlphaList 中各個 Alpha 狀態的分佈
        """
        distribution = {"complete": 0, "error": 0, "pending": 0, "running": 0, "unknown": 0}
        for alphaname in self.alpha_names:
            if os.path.exists(os.path.join(AlphaPath.COMPLETE, alphaname)):
                distribution["complete"] += 1
            elif os.path.exists(os.path.join(AlphaPath.ERROR, alphaname)):
                distribution["error"] += 1
            elif os.path.exists(os.path.join(AlphaPath.PENDING, alphaname)):
                distribution["pending"] += 1
            elif os.path.exists(os.path.join(AlphaPath.RUNNING, alphaname)):
                distribution["running"] += 1
            else:
                distribution["unknown"] += 1
        return distribution
        
def bcorr_filter(alphalist: AlphaList, score_scorer: AlphaScore, corr_threshold: float = 0.5) -> AlphaList:
    """
    根據 AlphaScore 排序並過濾掉高相關性的 alpha
    
    Args:
        alphalist: 輸入的 AlphaList
        score_scorer: 用來評分的 AlphaScore 物件
        corr_threshold: 相關係數閾值，大於此值的 alpha 會被踢除
        
    Returns:
        過濾後的 AlphaList，保留評分最高且相關性低於閾值的 alpha
    """
    
    # 1. 計算每個 alpha 的分數並排序
    alpha_scores = []
    for alpha in alphalist.alpha_list:
        try:
            score = score_scorer.score(alpha)
            if score is None:
                continue
            alpha_scores.append((alpha, score))
        except Exception:
            continue
    
    # 按分數從大到小排序
    alpha_scores.sort(key=lambda x: x[1], reverse=True)
    
    # 決定使用哪種方法：alpha 數量 > 50 時使用矩陣方法，否則用逐步方法
    if len(alpha_scores) > 100:
        return _bcorr_filter_matrix(alpha_scores, corr_threshold)
    else:
      return _bcorr_filter_stepwise(alpha_scores, corr_threshold)


def _bcorr_filter_matrix(alpha_scores: list, corr_threshold: float) -> AlphaList:
    """
    使用相關係數矩陣的批次過濾方法 - 適用於大量 alpha
    """
    print(f"使用矩陣方法處理 {len(alpha_scores)} 個 alpha")
    
    # 1. 構建 PNL DataFrame
    pnl_data = {}
    valid_alphas = []
    
    for i, (alpha, score) in enumerate(alpha_scores):
        pnl = alpha.get_pnl()
        if pnl is not None and len(pnl) > 0:
            pnl_data[i] = pnl
            valid_alphas.append((i, alpha, score))
    
    if not valid_alphas:
        return AlphaList()
    
    # 2. 建立對齊的 PNL DataFrame
    pnl_df = pd.DataFrame(pnl_data)
    
    # 3. 計算相關係數矩陣
    print("計算相關係數矩陣...")
    corr_matrix = pnl_df.corr()
    
    # 4. 貪婪選擇 - 根據分數順序選擇低相關的 alpha
    keep_indices = []
    pbar = tqdm(valid_alphas, desc="矩陣過濾中", unit="alpha")
    
    for idx, alpha, score in pbar:
        # 檢查與已選中的 alpha 的相關性
        should_keep = True
        for kept_idx in keep_indices:
            if abs(corr_matrix.loc[idx, kept_idx]) >= corr_threshold:
                should_keep = False
                break
        
        if should_keep:
            keep_indices.append(idx)
            pbar.set_description(f"矩陣過濾中 (已保留: {len(keep_indices)})")
    
    pbar.close()
    
    # 5. 建立結果 AlphaList
    filtered_alphalist = AlphaList()
    for idx, alpha, score in valid_alphas:
        if idx in keep_indices:
            filtered_alphalist.append(alpha)
    
    print(f"矩陣過濾結果: {len(alpha_scores)} -> {len(filtered_alphalist.alpha_list)} alphas")
    return filtered_alphalist


def _bcorr_filter_stepwise(alpha_scores: list, corr_threshold: float) -> AlphaList:
    """
    逐步貪婪去相關方法 - 適用於較少 alpha
    """
    print(f"使用逐步方法處理 {len(alpha_scores)} 個 alpha")
    
    # 2. 開始過濾流程 - 逐步貪婪去相關
    keep_alphas = []
    remaining_alphas = alpha_scores.copy()  # 剩餘待處理的 alpha
    
    pbar = tqdm(desc="bcorr_filter 過濾中", unit="alpha")
    while remaining_alphas:
        # 取出分數最高的 alpha
        best_alpha, best_score = remaining_alphas.pop(0)
        
        # 獲取最佳 alpha 的 pnl
        best_pnl = best_alpha.get_pnl()
        if best_pnl is None or len(best_pnl) == 0:
            pbar.update(1)
            continue
        
        # 保留這個最佳 alpha
        keep_alphas.append(best_alpha)
        pbar.update(1)
        
        # 過濾掉與最佳 alpha 相關性過高的其他 alpha
        filtered_alphas = []
        for alpha, score in remaining_alphas:
            current_pnl = alpha.get_pnl()
            if current_pnl is None or len(current_pnl) == 0:
                continue
            
            # 計算與最佳 alpha 的相關係數
            correlation = calculate_pnl_correlation(best_pnl, current_pnl)
            
            # 如果相關性低於門檻，保留此 alpha
            if correlation is None or abs(correlation) < corr_threshold:
                filtered_alphas.append((alpha, score))
        
        # 更新剩餘 alpha 清單
        remaining_alphas = filtered_alphas
        
        # 更新 tqdm 總數和描述
        pbar.total = len(keep_alphas) + len(remaining_alphas)
        pbar.set_description(f"bcorr_filter 過濾中 (已保留: {len(keep_alphas)})")
    
    pbar.close()
    
    # 3. 建立新的 AlphaList
    filtered_alphalist = AlphaList()
    for alpha in keep_alphas:
        filtered_alphalist.append(alpha)
    
    print(f"逐步過濾結果: {len(alpha_scores)} -> {len(filtered_alphalist.alpha_list)} alphas")
    
    return filtered_alphalist


def calculate_pnl_correlation(pnl1: pd.Series, pnl2: pd.Series) -> float:
    """
    計算兩個 PNL Series 的皮爾森相關係數
    
    Args:
        pnl1: 第一個 PNL Series
        pnl2: 第二個 PNL Series
        
    Returns:
        皮爾森相關係數，如果無法計算則回傳 None
    """
    try:
        # 按日期對齊兩個 pnl series
        # 找到共同的日期範圍
        common_dates = pnl1.index.intersection(pnl2.index)
        
        if len(common_dates) < 2:
            return None
        
        # 取得對齊的資料
        aligned_pnl1 = pnl1.loc[common_dates]
        aligned_pnl2 = pnl2.loc[common_dates]
        
        # 檢查是否有足夠的非空值
        valid_mask = ~(aligned_pnl1.isna() | aligned_pnl2.isna())
        if valid_mask.sum() < 2:
            return None
        
        # 計算皮爾森相關係數
        correlation = aligned_pnl1[valid_mask].corr(aligned_pnl2[valid_mask])
        
        return correlation
        
    except Exception:
        return None
