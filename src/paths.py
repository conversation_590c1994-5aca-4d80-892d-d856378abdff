from __future__ import annotations

import os


class AlphaPath:
    """集中管理 alpha 系統所需的資料夾路徑。"""

    ROOT: str = os.path.join("DB", "AlphaDB")

    # 目錄名稱常數
    PENDING: str = os.path.join(ROOT, "pending")
    RUNNING: str = os.path.join(ROOT, "running")
    COMPLETE: str = os.path.join(ROOT, "complete")
    ERROR: str = os.path.join(ROOT, "error")
    PNL: str = os.path.join(ROOT, "pnl")

    @classmethod
    def ensure_dirs(cls) -> None:
        """確保所有必要資料夾存在。"""
        for path in (cls.PENDING, cls.RUNNING, cls.COMPLETE, cls.ERROR, cls.PNL):
            os.makedirs(path, exist_ok=True)


# -------------------------------------------------
# Research-related資料夾


class ResearchPath:
    """集中管理研究流程相關資料夾路徑。"""

    ROOT: str = os.path.join("DB", "ResearchDB")

    TEMPLATE: str = os.path.join(ROOT, "template")
    SETTING: str = os.path.join(ROOT, "setting")
    GA_CONFIG: str = os.path.join(ROOT, "ga_config")
    RESEARCH_PROCESS: str = os.path.join(ROOT, "research_process")
    DATA: str = os.path.join(ROOT, "data")

    @classmethod
    def ensure_dirs(cls) -> None:
        for path in (cls.TEMPLATE, cls.SETTING, cls.GA_CONFIG, cls.RESEARCH_PROCESS, cls.DATA):
            os.makedirs(path, exist_ok=True)

AlphaPath.ensure_dirs()
ResearchPath.ensure_dirs()